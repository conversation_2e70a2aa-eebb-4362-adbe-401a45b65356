<template>
  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
    <div
      v-for="file in files"
      :key="file.id"
      class="file-card relative rounded-lg overflow-hidden transition-all duration-200"
      :class="{
        'selected': isFileSelected(file),
        'folder-card': file.type === 'folder',
        'file-card-regular': file.type !== 'folder'
      }"
      :data-type="file.type"
      :style="{
        backgroundColor: themeStore.isDark ? '#2a2a2a' : '#fff',
        borderColor: isFileSelected(file) ? themeColors.primary : themeColors.borderColor,
        boxShadow: isFileSelected(file) ? `0 0 0 2px ${themeColors.primary}` : 'none'
      }"
      @click="handleFileClick($event, file)"
      @contextmenu="handleContextMenu($event, file)"
    >
      <!-- 多选框 -->
      <div
        v-if="isMultiSelectMode"
        class="absolute top-2 left-2 z-10"
        @click.stop="handleCheckboxClick(file)"
      >
        <n-checkbox :checked="isFileSelected(file)" />
      </div>

      <!-- 标星按钮 -->
      <div
        class="absolute top-2 right-2 z-10 star-button"
        @click.stop="$emit('toggle-starred', file)"
      >
        <n-icon size="18" :color="file.starred ? '#f59e0b' : themeColors.iconColor">
          <component :is="file.starred ? StarSharp : StarOutline" />
        </n-icon>
      </div>

      <!-- 文件图标 -->
      <div class="file-icon-container flex items-center justify-center p-4" v-if="file.type == 'folder'">
        <div
          class="file-icon-wrapper rounded-lg p-3 transition-all duration-200"
          :class="{
            'folder-icon-wrapper': file.type === 'folder',
            'file-icon-wrapper-regular': file.type !== 'folder'
          }"
          :style="getIconWrapperStyle(file.type)"
        >
          <n-icon
            :size="file.type === 'folder' ? 48 : 40"
            :color="getFileColor(file.type)"
            class="transition-transform duration-200"
            :class="{ 'folder-icon': file.type === 'folder' }"
          >
            <component :is="getFileIcon(file.type)" />
          </n-icon>

          <!-- 文件夹特殊装饰 -->
          <div v-if="file.type === 'folder'" class="folder-decoration">
            <div class="folder-badge" :style="{ backgroundColor: getFileColor(file.type) }">
              <n-icon size="12" color="white">
                <FolderOpenOutline />
              </n-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件信息 -->
      <div class="file-info p-3 pt-0 text-center">
        <div
          class="file-name font-medium truncate mb-1 transition-colors duration-200"
          :class="{ 'folder-name': file.type === 'folder' }"
          :style="{ color: file.type === 'folder' ? getFileColor(file.type) : themeColors.textColor }"
        >
          {{ file.name }}
        </div>
        <div class="file-meta flex justify-center items-center text-xs" :style="{ color: themeColors.textSecondary }">
          <span>{{ file.size }}</span>
          <span class="mx-1" v-if="file.size">•</span>
          <span>{{ file.modified }}</span>
        </div>
      </div>

      <!-- 操作下拉菜单 -->
      <div class="absolute bottom-2 right-2 menu-button" @click.stop>
        <n-dropdown
          trigger="click"
          :options="processedDropdownOptions"
          @select="key => $emit('dropdown-select', key, file)"
          @click.stop
        >
          <div>
            <n-button quaternary circle size="small">
              <template #icon>
                <n-icon>
                  <EllipsisHorizontal />
                </n-icon>
              </template>
            </n-button>
          </div>
        </n-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, h,watch } from 'vue';
import { NIcon, NCheckbox, NButton, NDropdown } from 'naive-ui';
import { useThemeStore } from '../../stores/theme';
import {
  StarOutline,
  StarSharp,
  EllipsisHorizontal,
  FolderOutline,
  FolderOpenOutline,
  DocumentTextOutline,
  GridOutline,
  EaselOutline,
  DocumentOutline,
  ImageOutline,
  VideocamOutline,
  MusicalNotesOutline,
  ArchiveOutline,
  CodeSlashOutline,
  TerminalOutline,
  DocumentAttachOutline,
  ReaderOutline,
  CameraOutline,
  ColorPaletteOutline
} from '@vicons/ionicons5';

const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  fileIcons: {
    type: Object,
    required: true
  },
  fileColors: {
    type: Object,
    required: true
  },
  dropdownOptions: {
    type: Array,
    default: () => []
  },
  isMultiSelectMode: {
    type: Boolean,
    default: false
  },
  selectedFiles: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['select-file', 'toggle-starred', 'dropdown-select', 'select-multiple']);

const themeStore = useThemeStore();
const themeColors = computed(() => themeStore.getThemeColors());

// 处理下拉菜单选项，为图标添加正确的渲染函数
const processedDropdownOptions = computed(() => {
  return props.dropdownOptions.map(option => {
    if (option.type === 'divider') {
      return option;
    }
    
    return {
      ...option,
      icon: option.icon ? () => h(NIcon, null, { default: () => h(option.icon) }) : undefined
    };
  });
});

// 文件类型图标映射
const fileIconMap = {
  folder: FolderOutline,
  doc: DocumentTextOutline,
  docx: DocumentTextOutline,
  sheet: GridOutline,
  xlsx: GridOutline,
  xls: GridOutline,
  presentation: EaselOutline,
  pptx: EaselOutline,
  ppt: EaselOutline,
  pdf: ReaderOutline,
  txt: DocumentAttachOutline,
  text: DocumentAttachOutline,
  image: ImageOutline,
  jpg: CameraOutline,
  jpeg: CameraOutline,
  png: ImageOutline,
  gif: ColorPaletteOutline,
  svg: ColorPaletteOutline,
  video: VideocamOutline,
  mp4: VideocamOutline,
  avi: VideocamOutline,
  mov: VideocamOutline,
  audio: MusicalNotesOutline,
  mp3: MusicalNotesOutline,
  wav: MusicalNotesOutline,
  archive: ArchiveOutline,
  zip: ArchiveOutline,
  rar: ArchiveOutline,
  '7z': ArchiveOutline,
  tar: ArchiveOutline,
  gz: ArchiveOutline,
  code: CodeSlashOutline,
  js: CodeSlashOutline,
  ts: CodeSlashOutline,
  html: CodeSlashOutline,
  css: CodeSlashOutline,
  vue: CodeSlashOutline,
  py: CodeSlashOutline,
  java: CodeSlashOutline,
  cpp: CodeSlashOutline,
  c: CodeSlashOutline,
  exe: TerminalOutline,
  app: TerminalOutline,
  default: DocumentOutline
};

// 获取文件图标
const getFileIcon = (type) => {
  return fileIconMap[type] || fileIconMap.default;
};

// 获取文件颜色
const getFileColor = (type) => {
  return props.fileColors[type] || props.fileColors.default;
};

// 获取图标包装器样式
const getIconWrapperStyle = (type) => {
  const baseStyle = {
    backgroundColor: themeStore.isDark ? '#3a3a3a' : '#f5f5f5'
  };

  if (type === 'folder') {
    return {
      ...baseStyle,
      background: themeStore.isDark
        ? `linear-gradient(135deg, ${getFileColor(type)}20, ${getFileColor(type)}10)`
        : `linear-gradient(135deg, ${getFileColor(type)}15, ${getFileColor(type)}08)`,
      border: `1px solid ${getFileColor(type)}30`
    };
  }

  return baseStyle;
};

// 检查文件是否被选中
const isFileSelected = (file) => {
  return props.selectedFiles.some(f => f.id === file.id);
};

// 处理文件点击
const handleFileClick = (event, file) => {
  console.log('file',file)
  emit('select-file', file, event);
};

// 处理复选框点击
const handleCheckboxClick = (file) => {
  emit('select-multiple', file);
};

// 处理右键菜单
const handleContextMenu = (event, _file) => {
  event.preventDefault();
  // 这里可以添加右键菜单逻辑
};


watch(()=>props.files,(val)=>{
  console.log(val,'val')
},{
  immediate:true
})
</script>

<style scoped>
.file-card {
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
}

/* 文件夹卡片特殊样式 */
.folder-card {
  background: v-bind('themeStore.isDark ? "linear-gradient(135deg, #1e293b, #0f172a)" : "linear-gradient(135deg, #f8fafc, #f1f5f9)"');
}

.folder-card:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px v-bind('themeStore.isDark ? "rgba(59, 130, 246, 0.3)" : "rgba(59, 130, 246, 0.2)"');
}

/* 普通文件卡片样式 */
.file-card-regular:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px v-bind('themeStore.isDark ? "rgba(0, 0, 0, 0.3)" : "rgba(0, 0, 0, 0.1)"');
}

/* 文件夹图标包装器 */
.folder-icon-wrapper {
  position: relative;
  overflow: visible;
}

.folder-icon-wrapper:hover {
  transform: scale(1.1);
}

/* 文件夹图标动画 */
.folder-icon {
  transition: all 0.3s ease;
}

.folder-card:hover .folder-icon {
  transform: rotate(-5deg) scale(1.1);
}

/* 文件夹装饰 */
.folder-decoration {
  position: absolute;
  top: -4px;
  right: -4px;
}

.folder-badge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 文件夹名称样式 */
.folder-name {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 普通文件图标包装器 */
.file-icon-wrapper-regular:hover {
  transform: scale(1.05);
  background-color: v-bind('themeStore.isDark ? "#4a4a4a" : "#e5e7eb"');
}

.star-button,
.menu-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-card:hover .star-button,
.file-card:hover .menu-button,
.file-card.selected .star-button,
.file-card.selected .menu-button {
  opacity: 1;
}

.file-card.selected {
  border-width: 2px;
}

.file-icon-container {
  padding-top: 1.5rem;
}

/* 文件类型特殊效果 */
.file-card[data-type="image"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #7c3aed20, #7c3aed10);
  border: 1px solid #7c3aed30;
}

.file-card[data-type="video"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #ea580c20, #ea580c10);
  border: 1px solid #ea580c30;
}

.file-card[data-type="audio"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #0891b220, #0891b210);
  border: 1px solid #0891b230;
}

.file-card[data-type="doc"]:hover .file-icon-wrapper-regular,
.file-card[data-type="docx"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #2563eb20, #2563eb10);
  border: 1px solid #2563eb30;
}

.file-card[data-type="pdf"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #dc262620, #dc262610);
  border: 1px solid #dc262630;
}
</style>